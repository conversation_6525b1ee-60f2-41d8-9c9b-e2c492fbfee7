// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDsoxuTXhtOyQQIWZNDpyiWfOw6XgK5F8Y',
    appId: '1:1025540647070:web:af708cc3962933eac9738f',
    messagingSenderId: '1025540647070',
    projectId: 'smartlab-e2107',
    authDomain: 'smartlab-e2107.firebaseapp.com',
    storageBucket: 'smartlab-e2107.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB6pXh0wrPfOz6z0cy8iRm9dYkEjR3ptQ0',
    appId: '1:1025540647070:android:410e53820c12b86ec9738f',
    messagingSenderId: '1025540647070',
    projectId: 'smartlab-e2107',
    storageBucket: 'smartlab-e2107.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAmSZ-XXLzs-c3OfaVDlICo1qv2GsOvxpU',
    appId: '1:1025540647070:ios:48daf25efd621969c9738f',
    messagingSenderId: '1025540647070',
    projectId: 'smartlab-e2107',
    storageBucket: 'smartlab-e2107.firebasestorage.app',
    iosBundleId: 'com.example.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAmSZ-XXLzs-c3OfaVDlICo1qv2GsOvxpU',
    appId: '1:1025540647070:ios:48daf25efd621969c9738f',
    messagingSenderId: '1025540647070',
    projectId: 'smartlab-e2107',
    storageBucket: 'smartlab-e2107.firebasestorage.app',
    iosBundleId: 'com.example.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDsoxuTXhtOyQQIWZNDpyiWfOw6XgK5F8Y',
    appId: '1:1025540647070:web:b4aaee05799d60b9c9738f',
    messagingSenderId: '1025540647070',
    projectId: 'smartlab-e2107',
    authDomain: 'smartlab-e2107.firebaseapp.com',
    storageBucket: 'smartlab-e2107.firebasestorage.app',
  );
}
